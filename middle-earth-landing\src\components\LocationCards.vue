<template>
  <section class="py-20 bg-gradient-to-b from-slate-900 to-slate-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-elvish font-bold text-gradient mb-6">
          Legendary Destinations
        </h2>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto font-hobbit">
          Explore the most iconic locations across Middle Earth. Each destination offers unique experiences, 
          breathtaking landscapes, and unforgettable adventures.
        </p>
      </div>

      <!-- Location Cards Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- The Shire -->
        <div class="group relative bg-gradient-to-br from-middle-earth-shire/20 to-green-900/30 rounded-2xl p-6 border border-green-500/30 card-hover overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-green-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10">
            <div class="w-16 h-16 bg-middle-earth-shire rounded-full flex items-center justify-center mb-4 group-hover:animate-bounce">
              <span class="text-2xl">🏡</span>
            </div>
            <h3 class="text-2xl font-elvish font-bold text-middle-earth-shire mb-3">The Shire</h3>
            <p class="text-gray-300 mb-4 font-hobbit">
              A peaceful land of rolling green hills, cozy hobbit-holes, and the finest pipe-weed in all of Middle Earth.
            </p>
            <ul class="space-y-2 text-sm text-gray-400 mb-6">
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                <span>Bag End Tours</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                <span>Green Dragon Inn</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                <span>Hobbiton Market</span>
              </li>
            </ul>
            <button class="w-full px-4 py-2 bg-middle-earth-shire text-black font-semibold rounded-lg hover:bg-green-400 transition-colors">
              Visit The Shire
            </button>
          </div>
        </div>

        <!-- Rivendell -->
        <div class="group relative bg-gradient-to-br from-middle-earth-rivendell/20 to-purple-900/30 rounded-2xl p-6 border border-purple-500/30 card-hover overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10">
            <div class="w-16 h-16 bg-middle-earth-rivendell rounded-full flex items-center justify-center mb-4 group-hover:animate-bounce">
              <span class="text-2xl">🏛️</span>
            </div>
            <h3 class="text-2xl font-elvish font-bold text-middle-earth-rivendell mb-3">Rivendell</h3>
            <p class="text-gray-300 mb-4 font-hobbit">
              The Last Homely House, where elven wisdom flows like the waterfalls and time moves differently.
            </p>
            <ul class="space-y-2 text-sm text-gray-400 mb-6">
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-purple-400 rounded-full"></span>
                <span>Council of Elrond</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-purple-400 rounded-full"></span>
                <span>Elven Libraries</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-purple-400 rounded-full"></span>
                <span>Healing Gardens</span>
              </li>
            </ul>
            <button class="w-full px-4 py-2 bg-middle-earth-rivendell text-black font-semibold rounded-lg hover:bg-purple-300 transition-colors">
              Enter Rivendell
            </button>
          </div>
        </div>

        <!-- Rohan -->
        <div class="group relative bg-gradient-to-br from-middle-earth-rohan/20 to-orange-900/30 rounded-2xl p-6 border border-orange-500/30 card-hover overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10">
            <div class="w-16 h-16 bg-middle-earth-rohan rounded-full flex items-center justify-center mb-4 group-hover:animate-bounce">
              <span class="text-2xl">🐎</span>
            </div>
            <h3 class="text-2xl font-elvish font-bold text-orange-300 mb-3">Rohan</h3>
            <p class="text-gray-300 mb-4 font-hobbit">
              Land of the horse-lords, where the Rohirrim ride across endless grasslands under vast skies.
            </p>
            <ul class="space-y-2 text-sm text-gray-400 mb-6">
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-orange-400 rounded-full"></span>
                <span>Edoras Golden Hall</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-orange-400 rounded-full"></span>
                <span>Horse Riding Tours</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-orange-400 rounded-full"></span>
                <span>Helm's Deep</span>
              </li>
            </ul>
            <button class="w-full px-4 py-2 bg-orange-400 text-black font-semibold rounded-lg hover:bg-orange-300 transition-colors">
              Ride to Rohan
            </button>
          </div>
        </div>

        <!-- Gondor -->
        <div class="group relative bg-gradient-to-br from-middle-earth-gondor/20 to-blue-900/30 rounded-2xl p-6 border border-blue-500/30 card-hover overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative z-10">
            <div class="w-16 h-16 bg-middle-earth-gondor rounded-full flex items-center justify-center mb-4 group-hover:animate-bounce">
              <span class="text-2xl">👑</span>
            </div>
            <h3 class="text-2xl font-elvish font-bold text-blue-300 mb-3">Gondor</h3>
            <p class="text-gray-300 mb-4 font-hobbit">
              The greatest kingdom of Men, with the white city of Minas Tirith standing as a beacon of hope.
            </p>
            <ul class="space-y-2 text-sm text-gray-400 mb-6">
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                <span>Minas Tirith Tours</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                <span>Royal Gardens</span>
              </li>
              <li class="flex items-center space-x-2">
                <span class="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                <span>Citadel Library</span>
              </li>
            </ul>
            <button class="w-full px-4 py-2 bg-blue-400 text-black font-semibold rounded-lg hover:bg-blue-300 transition-colors">
              Enter Gondor
            </button>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      <div class="text-center mt-16">
        <p class="text-gray-300 mb-6 font-hobbit">
          Can't decide? Let our expert guides help you choose the perfect adventure.
        </p>
        <button class="px-8 py-4 bg-middle-earth-gold text-black font-bold rounded-lg hover:bg-yellow-400 transform hover:scale-105 transition-all duration-300 ring-glow">
          Get Personalized Recommendations
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Component logic can be added here if needed
</script>

<style scoped>
/* Additional component-specific styles */
</style>
