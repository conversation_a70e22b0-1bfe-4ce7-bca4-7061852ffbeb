<template>
  <section class="relative min-h-screen flex items-center justify-center overflow-hidden hero-gradient">
    <!-- Animated background particles -->
    <div class="absolute inset-0">
      <div v-for="i in 50" :key="i" 
           class="absolute w-1 h-1 bg-middle-earth-gold rounded-full animate-pulse"
           :style="{
             left: Math.random() * 100 + '%',
             top: Math.random() * 100 + '%',
             animationDelay: Math.random() * 3 + 's',
             animationDuration: (2 + Math.random() * 3) + 's'
           }">
      </div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Left side - Hero content -->
        <div class="space-y-8">
          <h1 class="text-5xl md:text-7xl font-elvish font-bold text-gradient animate-float">
            Discover the Wonders of Middle Earth
          </h1>
          
          <p class="text-xl md:text-2xl text-gray-300 font-hobbit leading-relaxed">
            Embark on an epic journey through the most magical realm ever created. 
            From the peaceful Shire to the majestic halls of Rivendell, 
            experience the adventure of a lifetime.
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="px-8 py-4 bg-middle-earth-gold text-black font-bold rounded-lg hover:bg-yellow-400 transform hover:scale-105 transition-all duration-300 ring-glow">
              Start Your Quest
            </button>
            <button class="px-8 py-4 border-2 border-middle-earth-gold text-middle-earth-gold font-bold rounded-lg hover:bg-middle-earth-gold hover:text-black transition-all duration-300">
              Watch Preview
            </button>
          </div>

          <!-- Trust indicators -->
          <div class="flex items-center justify-center space-x-8 text-gray-400 text-sm">
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>1M+ Adventurers</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-blue-400 rounded-full"></span>
              <span>Trusted by Wizards</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
              <span>Epic Guaranteed</span>
            </div>
          </div>
        </div>

        <!-- Right side - Journey Request Form -->
        <div class="bg-black/30 backdrop-blur-md rounded-2xl p-8 border border-middle-earth-gold/30 card-hover">
          <h3 class="text-2xl font-elvish font-bold text-middle-earth-gold mb-6 text-center">
            Request Your Journey
          </h3>
          
          <form @submit.prevent="submitForm" class="space-y-6">
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Your Name</label>
              <input 
                v-model="form.name"
                type="text" 
                required
                class="w-full px-4 py-3 bg-black/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-middle-earth-gold focus:ring-1 focus:ring-middle-earth-gold transition-colors"
                placeholder="Enter your name, brave adventurer"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Email Address</label>
              <input 
                v-model="form.email"
                type="email" 
                required
                class="w-full px-4 py-3 bg-black/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-middle-earth-gold focus:ring-1 focus:ring-middle-earth-gold transition-colors"
                placeholder="<EMAIL>"
              >
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Preferred Destination</label>
              <select 
                v-model="form.destination"
                class="w-full px-4 py-3 bg-black/50 border border-gray-600 rounded-lg text-white focus:border-middle-earth-gold focus:ring-1 focus:ring-middle-earth-gold transition-colors"
              >
                <option value="">Choose your destination</option>
                <option value="shire">The Shire</option>
                <option value="rivendell">Rivendell</option>
                <option value="rohan">Rohan</option>
                <option value="gondor">Gondor</option>
                <option value="moria">Mines of Moria</option>
                <option value="isengard">Isengard</option>
              </select>
            </div>

            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">Adventure Type</label>
              <div class="grid grid-cols-2 gap-3">
                <label class="flex items-center space-x-2 cursor-pointer">
                  <input v-model="form.adventureType" type="radio" value="peaceful" class="text-middle-earth-gold">
                  <span class="text-gray-300">Peaceful</span>
                </label>
                <label class="flex items-center space-x-2 cursor-pointer">
                  <input v-model="form.adventureType" type="radio" value="epic" class="text-middle-earth-gold">
                  <span class="text-gray-300">Epic Quest</span>
                </label>
              </div>
            </div>

            <button 
              type="submit"
              :disabled="isSubmitting"
              class="w-full px-6 py-4 bg-middle-earth-gold text-black font-bold rounded-lg hover:bg-yellow-400 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ isSubmitting ? 'Preparing Your Journey...' : 'Begin My Adventure' }}
            </button>

            <p class="text-xs text-gray-400 text-center">
              By submitting, you agree to embark on an epic journey. 
              <br>No orcs or dragons guaranteed to be friendly.
            </p>
          </form>
        </div>
      </div>
    </div>

    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <div class="w-6 h-10 border-2 border-middle-earth-gold rounded-full flex justify-center">
        <div class="w-1 h-3 bg-middle-earth-gold rounded-full mt-2 animate-pulse"></div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const isSubmitting = ref(false)

const form = reactive({
  name: '',
  email: '',
  destination: '',
  adventureType: ''
})

const submitForm = async () => {
  isSubmitting.value = true
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  alert(`Welcome ${form.name}! Your journey to ${form.destination} has been requested. A wizard will contact you shortly via ${form.email}.`)
  
  // Reset form
  Object.assign(form, {
    name: '',
    email: '',
    destination: '',
    adventureType: ''
  })
  
  isSubmitting.value = false
}
</script>

<style scoped>
/* Additional component-specific styles can go here */
</style>
